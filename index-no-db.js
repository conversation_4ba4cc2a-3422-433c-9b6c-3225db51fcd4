// Import the express library
const express = require('express');

// Create an instance of an Express application
const app = express();
const port = 3000;

// Mock data to simulate database results
const mockData = [
    { TABLE_NAME: 'users' },
    { TABLE_NAME: 'products' },
    { TABLE_NAME: 'orders' },
    { TABLE_NAME: 'categories' }
];

// Define a route for the root URL ('/')
app.get('/', async (req, res) => {
    try {
        // Return mock data instead of database query
        console.log('Returning mock table data');
        res.json(mockData);
    } catch (err) {
        console.error('Error:', err);
        res.status(500).send({ message: 'Server error', error: err.message });
    }
});

// Start the server
app.listen(port, () => {
    console.log(`Mock database app listening at http://localhost:${port}`);
    console.log('This version works without requiring SQL Server connection');
});
