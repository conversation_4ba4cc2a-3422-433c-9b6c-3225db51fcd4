{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/poller/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Configurations for how to poll the operation and to check whether it has\n * terminated.\n */\nexport interface OperationConfig {\n  /** The operation location */\n  operationLocation?: string;\n  /** The resource location */\n  resourceLocation?: string;\n  /** metadata about the operation */\n  metadata?: Record<string, string>;\n}\n\n/**\n * The description of an operation.\n */\nexport interface Operation<TResponse, TOptions> {\n  /**\n   * Sends the initiation request and returns, in addition to the response, the\n   * operation location, the potential resource location, and a set of metadata.\n   */\n  init: () => Promise<\n    OperationConfig & {\n      response: TResponse;\n    }\n  >;\n  /**\n   * Sends the polling request.\n   */\n  poll: (location: string, options?: TOptions) => Promise<TResponse>;\n}\n\n/**\n * Type of a restorable long-running operation.\n */\nexport type RestorableOperationState<T> = T & {\n  /** The operation configuration */\n  config: OperationConfig;\n};\n\n/**\n * Options for `createPoller`.\n */\nexport interface CreatePollerOptions<TResponse, TResult, TState> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller which can be used to resume an existing paused Long-Running-Operation.\n   */\n  restoreFrom?: string;\n  /**\n   * A function to process the result of the LRO.\n   */\n  processResult?: (result: TResponse, state: TState) => TResult;\n  /**\n   * A function to process the state of the LRO.\n   */\n  updateState?: (state: TState, lastResponse: TResponse) => void;\n  /**\n   * A function to be called each time the operation location is updated by the\n   * service.\n   */\n  withOperationLocation?: (operationLocation: string) => void;\n}\n\nexport interface LroError {\n  code: string;\n  innererror?: InnerError;\n  message: string;\n}\n\nexport interface InnerError {\n  code: string;\n  message: string;\n  innererror?: InnerError;\n}\n\n/**\n * Options for `buildCreatePoller`.\n */\nexport interface BuildCreatePollerOptions<TResponse, TState> {\n  /**\n   * Gets the status of the operation from the response received when the\n   * operation was initialized. Note that the operation could be already in\n   * a terminal state at this time.\n   */\n  getStatusFromInitialResponse: (inputs: {\n    response: TResponse;\n    state: RestorableOperationState<TState>;\n    operationLocation?: string;\n  }) => OperationStatus;\n  /**\n   * Gets the status of the operation from a response received when the\n   * operation was polled.\n   */\n  getStatusFromPollResponse: (\n    response: TResponse,\n    state: RestorableOperationState<TState>,\n  ) => OperationStatus;\n  /**\n   * Determines if the input error is an operation error.\n   */\n  isOperationError: (error: Error) => boolean;\n  /**\n   * Gets the updated operation location from polling responses.\n   */\n  getOperationLocation?: (\n    response: TResponse,\n    state: RestorableOperationState<TState>,\n  ) => string | undefined;\n  /**\n   * Gets the resource location from a response.\n   */\n  getResourceLocation: (\n    response: TResponse,\n    state: RestorableOperationState<TState>,\n  ) => string | undefined;\n  /**\n   * Gets from the response the time interval the service suggests the client to\n   * wait before sending the next polling request.\n   */\n  getPollingInterval?: (response: TResponse) => number | undefined;\n  /**\n   * Extracts an error model from a response.\n   */\n  getError?: (response: TResponse) => LroError | undefined;\n  /**\n   * Control whether to throw an exception if the operation failed or was canceled.\n   */\n  resolveOnUnsuccessful: boolean;\n}\n\n/**\n * The set of possible states an operation can be in at any given time.\n */\nexport type OperationStatus = \"notStarted\" | \"running\" | \"succeeded\" | \"canceled\" | \"failed\";\n\n/**\n * While the poller works as the local control mechanism to start triggering and\n * wait for a long-running operation, OperationState documents the status of\n * the remote long-running operation. It gets updated after each poll.\n */\nexport interface OperationState<TResult> {\n  /**\n   * The current status of the operation.\n   */\n  status: OperationStatus;\n  /**\n   * Will exist if the operation encountered any error.\n   */\n  error?: Error;\n  /**\n   * Will exist if the operation produced a result of the expected type.\n   */\n  result?: TResult;\n}\n\n/**\n * CancelOnProgress is used as the return value of a Poller's onProgress method.\n * When a user invokes onProgress, they're required to pass in a function that will be\n * called as a callback with the new data received each time the poll operation is updated.\n * onProgress returns a function that will prevent any further update to reach the original callback.\n */\nexport type CancelOnProgress = () => void;\n\n/**\n * A simple poller interface.\n */\nexport interface SimplePollerLike<TState extends OperationState<TResult>, TResult> {\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   */\n  poll(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  pollUntilDone(pollOptions?: { abortSignal?: AbortSignalLike }): Promise<TResult>;\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  onProgress(callback: (state: TState) => void): CancelOnProgress;\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  isDone(): boolean;\n  /**\n   * Stops the poller. After this, no manual or automated requests can be sent.\n   */\n  stopPolling(): void;\n  /**\n   * Returns true if the poller is stopped.\n   */\n  isStopped(): boolean;\n  /**\n   * Returns the state of the operation.\n   */\n  getOperationState(): TState;\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  getResult(): TResult | undefined;\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  toString(): string;\n}\n\n/**\n * A state proxy that allows poller implementation to abstract away the operation\n * state. This is useful to implement `lroEngine` and `createPoller` in a modular\n * way.\n */\nexport interface StateProxy<TState, TResult> {\n  initState: (config: OperationConfig) => RestorableOperationState<TState>;\n\n  setRunning: (state: TState) => void;\n  setCanceled: (state: TState) => void;\n  setResult: (state: TState, result: TResult) => void;\n  setError: (state: TState, error: Error) => void;\n  setFailed: (state: TState) => void;\n  setSucceeded: (state: TState) => void;\n\n  isRunning: (state: TState) => boolean;\n  isCanceled: (state: TState) => boolean;\n  getResult: (state: TState) => TResult | undefined;\n  getError: (state: TState) => Error | undefined;\n  isFailed: (state: TState) => boolean;\n  isSucceeded: (state: TState) => boolean;\n}\n"]}