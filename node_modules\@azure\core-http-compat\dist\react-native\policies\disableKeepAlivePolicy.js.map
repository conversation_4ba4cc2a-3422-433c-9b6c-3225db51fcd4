{"version": 3, "file": "disableKeepAlivePolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/disableKeepAlivePolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAUlC,MAAM,CAAC,MAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAEnE,MAAM,UAAU,4BAA4B;IAC1C,OAAO;QACL,IAAI,EAAE,0BAA0B;QAChC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAChC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sCAAsC,CAAC,QAAkB;IACvE,OAAO,QAAQ,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,0BAA0B,CAAC,CAAC;AACpG,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  Pipeline,\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\n\nexport const disableKeepAlivePolicyName = \"DisableKeepAlivePolicy\";\n\nexport function createDisableKeepAlivePolicy(): PipelinePolicy {\n  return {\n    name: disableKeepAlivePolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      request.disableKeepAlive = true;\n      return next(request);\n    },\n  };\n}\n\n/**\n * @internal\n */\nexport function pipelineContainsDisableKeepAlivePolicy(pipeline: Pipeline): boolean {\n  return pipeline.getOrderedPolicies().some((policy) => policy.name === disableKeepAlivePolicyName);\n}\n"]}