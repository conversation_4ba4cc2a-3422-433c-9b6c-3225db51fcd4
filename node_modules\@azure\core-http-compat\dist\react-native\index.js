// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * A Shim Library that provides compatibility between Core V1 & V2 Packages.
 *
 * @packageDocumentation
 */
export { ExtendedServiceClient, } from "./extendedClient.js";
export { requestPolicyFactoryPolicyName, createRequestPolicyFactoryPolicy, HttpPipelineLogLevel, } from "./policies/requestPolicyFactoryPolicy.js";
export { disableKeepAlivePolicyName } from "./policies/disableKeepAlivePolicy.js";
export { convertHttpClient } from "./httpClientAdapter.js";
export { toHttpHeadersLike, } from "./util.js";
//# sourceMappingURL=index.js.map