{"version": 3, "file": "poller.d.ts", "sourceRoot": "", "sources": ["../../../src/legacy/poller.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC;;;;GAIG;AACH,MAAM,MAAM,oBAAoB,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;AAEnE;;;GAGG;AACH,qBAAa,kBAAmB,SAAQ,KAAK;gBAC/B,OAAO,EAAE,MAAM;CAK5B;AAED;;;GAGG;AACH,qBAAa,oBAAqB,SAAQ,KAAK;gBACjC,OAAO,EAAE,MAAM;CAK5B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4DG;AAEH,8BAAsB,MAAM,CAAC,MAAM,SAAS,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,CAC9E,YAAW,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;IAEtC,kFAAkF;IAClF,SAAS,CAAC,qBAAqB,EAAE,OAAO,CAAS;IACjD,OAAO,CAAC,OAAO,CAAiB;IAChC,OAAO,CAAC,OAAO,CAAC,CAA2B;IAC3C,OAAO,CAAC,MAAM,CAAC,CAAqE;IACpF,OAAO,CAAC,eAAe,CAAC,CAAgB;IACxC,OAAO,CAAC,aAAa,CAAC,CAAgB;IACtC,OAAO,CAAC,OAAO,CAAmB;IAClC,OAAO,CAAC,qBAAqB,CAAsC;IAEnE;;;OAGG;IACH,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAEpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgEG;gBACS,SAAS,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC;IAmBrD;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAEzC;;;OAGG;YACW,YAAY;IAU1B;;;;;;;OAOG;YACW,QAAQ;IAUtB;;;;;;;OAOG;IACH,OAAO,CAAC,YAAY;IAMpB;;OAEG;YACW,UAAU;IAIxB;;;;;;;OAOG;IACI,IAAI,CAAC,OAAO,GAAE;QAAE,WAAW,CAAC,EAAE,eAAe,CAAA;KAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAW3E,OAAO,CAAC,mBAAmB;IA0B3B;;OAEG;IACU,aAAa,CACxB,WAAW,GAAE;QAAE,WAAW,CAAC,EAAE,eAAe,CAAA;KAAO,GAClD,OAAO,CAAC,OAAO,CAAC;IAUnB;;;;;OAKG;IACI,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,gBAAgB;IAOtE;;OAEG;IACI,MAAM,IAAI,OAAO;IAKxB;;OAEG;IACI,WAAW,IAAI,IAAI;IAS1B;;OAEG;IACI,SAAS,IAAI,OAAO;IAI3B;;;;;;;;OAQG;IACI,eAAe,CAAC,OAAO,GAAE;QAAE,WAAW,CAAC,EAAE,eAAe,CAAA;KAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAStF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACI,iBAAiB,IAAI,MAAM;IAIlC;;;;;OAKG;IACI,SAAS,IAAI,OAAO,GAAG,SAAS;IAKvC;;;OAGG;IACI,QAAQ,IAAI,MAAM;CAG1B"}