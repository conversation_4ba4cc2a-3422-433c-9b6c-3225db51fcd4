{"version": 3, "file": "index.js", "sources": ["../src/AbortSignal.ts", "../src/AbortController.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference path=\"../shims-public.d.ts\" />\n\ntype AbortEventListener = (this: AbortSignalLike, ev?: any) => any;\n\nconst listenersMap = new WeakMap<AbortSignal, AbortEventListener[]>();\nconst abortedMap = new WeakMap<AbortSignal, boolean>();\n\n/**\n * Allows the request to be aborted upon firing of the \"abort\" event.\n * Compatible with the browser built-in AbortSignal and common polyfills.\n */\nexport interface AbortSignalLike {\n  /**\n   * Indicates if the signal has already been aborted.\n   */\n  readonly aborted: boolean;\n  /**\n   * Add new \"abort\" event listener, only support \"abort\" event.\n   */\n  addEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   */\n  removeEventListener(\n    type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any,\n    options?: any\n  ): void;\n}\n\n/**\n * An aborter instance implements AbortSignal interface, can abort HTTP requests.\n *\n * - Call AbortSignal.none to create a new AbortSignal instance that cannot be cancelled.\n * Use `AbortSignal.none` when you are required to pass a cancellation token but the operation\n * cannot or will not ever be cancelled.\n *\n * @example\n * Abort without timeout\n * ```ts\n * await doAsyncWork(AbortSignal.none);\n * ```\n */\nexport class AbortSignal implements AbortSignalLike {\n  constructor() {\n    listenersMap.set(this, []);\n    abortedMap.set(this, false);\n  }\n\n  /**\n   * Status of whether aborted or not.\n   *\n   * @readonly\n   */\n  public get aborted(): boolean {\n    if (!abortedMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    return abortedMap.get(this)!;\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will never be aborted.\n   *\n   * @readonly\n   */\n  public static get none(): AbortSignal {\n    return new AbortSignal();\n  }\n\n  /**\n   * onabort event listener.\n   */\n  public onabort: ((ev?: Event) => any) | null = null;\n\n  /**\n   * Added new \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be added\n   */\n  public addEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n    listeners.push(listener);\n  }\n\n  /**\n   * Remove \"abort\" event listener, only support \"abort\" event.\n   *\n   * @param _type - Only support \"abort\" event\n   * @param listener - The listener to be removed\n   */\n  public removeEventListener(\n    // tslint:disable-next-line:variable-name\n    _type: \"abort\",\n    listener: (this: AbortSignalLike, ev: any) => any\n  ): void {\n    if (!listenersMap.has(this)) {\n      throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n    }\n\n    const listeners = listenersMap.get(this)!;\n\n    const index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Dispatches a synthetic event to the AbortSignal.\n   */\n  dispatchEvent(_event: Event): boolean {\n    throw new Error(\n      \"This is a stub dispatchEvent implementation that should not be used.  It only exists for type-checking purposes.\"\n    );\n  }\n}\n\n/**\n * Helper to trigger an abort event immediately, the onabort and all abort event listeners will be triggered.\n * Will try to trigger abort event for all linked AbortSignal nodes.\n *\n * - If there is a timeout, the timer will be cancelled.\n * - If aborted is true, nothing will happen.\n *\n * @internal\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-use-interface-parameters\nexport function abortSignal(signal: AbortSignal): void {\n  if (signal.aborted) {\n    return;\n  }\n\n  if (signal.onabort) {\n    signal.onabort.call(signal);\n  }\n\n  const listeners = listenersMap.get(signal)!;\n  if (listeners) {\n    // Create a copy of listeners so mutations to the array\n    // (e.g. via removeListener calls) don't affect the listeners\n    // we invoke.\n    listeners.slice().forEach((listener) => {\n      listener.call(signal, { type: \"abort\" });\n    });\n  }\n\n  abortedMap.set(signal, true);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignal, AbortSignalLike, abortSignal } from \"./AbortSignal\";\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nexport class AbortError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = \"AbortError\";\n  }\n}\n\n/**\n * An AbortController provides an AbortSignal and the associated controls to signal\n * that an asynchronous operation should be aborted.\n *\n * @example\n * Abort an operation when another event fires\n * ```ts\n * const controller = new AbortController();\n * const signal = controller.signal;\n * doAsyncWork(signal);\n * button.addEventListener('click', () => controller.abort());\n * ```\n *\n * @example\n * Share aborter cross multiple operations in 30s\n * ```ts\n * // Upload the same data to 2 different data centers at the same time,\n * // abort another when any of them is finished\n * const controller = AbortController.withTimeout(30 * 1000);\n * doAsyncWork(controller.signal).then(controller.abort);\n * doAsyncWork(controller.signal).then(controller.abort);\n *```\n *\n * @example\n * Cascaded aborting\n * ```ts\n * // All operations can't take more than 30 seconds\n * const aborter = Aborter.timeout(30 * 1000);\n *\n * // Following 2 operations can't take more than 25 seconds\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * ```\n */\nexport class AbortController {\n  private _signal: AbortSignal;\n\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(parentSignals?: AbortSignalLike[]);\n  /**\n   * @param parentSignals - The AbortSignals that will signal aborted on the AbortSignal associated with this controller.\n   */\n  constructor(...parentSignals: AbortSignalLike[]);\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n  constructor(parentSignals?: any) {\n    this._signal = new AbortSignal();\n\n    if (!parentSignals) {\n      return;\n    }\n    // coerce parentSignals into an array\n    if (!Array.isArray(parentSignals)) {\n      // eslint-disable-next-line prefer-rest-params\n      parentSignals = arguments;\n    }\n    for (const parentSignal of parentSignals) {\n      // if the parent signal has already had abort() called,\n      // then call abort on this signal as well.\n      if (parentSignal.aborted) {\n        this.abort();\n      } else {\n        // when the parent signal aborts, this signal should as well.\n        parentSignal.addEventListener(\"abort\", () => {\n          this.abort();\n        });\n      }\n    }\n  }\n\n  /**\n   * The AbortSignal associated with this controller that will signal aborted\n   * when the abort method is called on this controller.\n   *\n   * @readonly\n   */\n  public get signal(): AbortSignal {\n    return this._signal;\n  }\n\n  /**\n   * Signal that any operations passed this controller's associated abort signal\n   * to cancel any remaining work and throw an `AbortError`.\n   */\n  abort(): void {\n    abortSignal(this._signal);\n  }\n\n  /**\n   * Creates a new AbortSignal instance that will abort after the provided ms.\n   * @param ms - Elapsed time in milliseconds to trigger an abort.\n   */\n  public static timeout(ms: number): AbortSignal {\n    const signal = new AbortSignal();\n    const timer = setTimeout(abortSignal, ms, signal);\n    // Prevent the active Timer from keeping the Node.js event loop active.\n    if (typeof timer.unref === \"function\") {\n      timer.unref();\n    }\n    return signal;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAGA;AAIA,MAAM,YAAY,GAAG,IAAI,OAAO,EAAqC,CAAC;AACtE,MAAM,UAAU,GAAG,IAAI,OAAO,EAAwB,CAAC;AA6BvD;;;;;;;;;;;;AAYG;MACU,WAAW,CAAA;AACtB,IAAA,WAAA,GAAA;AA2BA;;AAEG;QACI,IAAO,CAAA,OAAA,GAAiC,IAAI,CAAC;AA7BlD,QAAA,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC3B,QAAA,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7B;AAED;;;;AAIG;AACH,IAAA,IAAW,OAAO,GAAA;AAChB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,OAAO,UAAU,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;KAC9B;AAED;;;;AAIG;AACI,IAAA,WAAW,IAAI,GAAA;QACpB,OAAO,IAAI,WAAW,EAAE,CAAC;KAC1B;AAOD;;;;;AAKG;IACI,gBAAgB;;AAErB,IAAA,KAAc,EACd,QAAiD,EAAA;AAEjD,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC1E,SAAA;QAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;AAC1C,QAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC1B;AAED;;;;;AAKG;IACI,mBAAmB;;AAExB,IAAA,KAAc,EACd,QAAiD,EAAA;AAEjD,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;AAC1E,SAAA;QAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAE1C,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1C,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,YAAA,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5B,SAAA;KACF;AAED;;AAEG;AACH,IAAA,aAAa,CAAC,MAAa,EAAA;AACzB,QAAA,MAAM,IAAI,KAAK,CACb,kHAAkH,CACnH,CAAC;KACH;AACF,CAAA;AAED;;;;;;;;AAQG;AACH;AACM,SAAU,WAAW,CAAC,MAAmB,EAAA;IAC7C,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,OAAO;AACR,KAAA;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,QAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7B,KAAA;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;AAC5C,IAAA,IAAI,SAAS,EAAE;;;;QAIb,SAAS,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;YACrC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/B;;ACtKA;AAKA;;;;;;;;;;;;;;;;;AAiBG;AACG,MAAO,UAAW,SAAQ,KAAK,CAAA;AACnC,IAAA,WAAA,CAAY,OAAgB,EAAA;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;KAC1B;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;MACU,eAAe,CAAA;;AAY1B,IAAA,WAAA,CAAY,aAAmB,EAAA;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAEjC,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO;AACR,SAAA;;AAED,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;;YAEjC,aAAa,GAAG,SAAS,CAAC;AAC3B,SAAA;AACD,QAAA,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;;;YAGxC,IAAI,YAAY,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;AACd,aAAA;AAAM,iBAAA;;AAEL,gBAAA,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;oBAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;AACf,iBAAC,CAAC,CAAC;AACJ,aAAA;AACF,SAAA;KACF;AAED;;;;;AAKG;AACH,IAAA,IAAW,MAAM,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED;;;AAGG;IACH,KAAK,GAAA;AACH,QAAA,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3B;AAED;;;AAGG;IACI,OAAO,OAAO,CAAC,EAAU,EAAA;AAC9B,QAAA,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QACjC,MAAM,KAAK,GAAG,UAAU,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;;AAElD,QAAA,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,UAAU,EAAE;YACrC,KAAK,CAAC,KAAK,EAAE,CAAC;AACf,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACF;;;;;;"}