/**
 * A Shim Library that provides compatibility between Core V1 & V2 Packages.
 *
 * @packageDocumentation
 */
export { ExtendedServiceClient, ExtendedServiceClientOptions, ExtendedCommonClientOptions, ExtendedClientOptions, } from "./extendedClient.js";
export { CompatResponse } from "./response.js";
export { requestPolicyFactoryPolicyName, createRequestPolicyFactoryPolicy, RequestPolicyFactory, RequestPolicy, RequestPolicyOptionsLike, HttpPipelineLogLevel, } from "./policies/requestPolicyFactoryPolicy.js";
export { KeepAliveOptions } from "./policies/keepAliveOptions.js";
export { RedirectOptions } from "./policies/redirectOptions.js";
export { disableKeepAlivePolicyName } from "./policies/disableKeepAlivePolicy.js";
export { convertHttpClient } from "./httpClientAdapter.js";
export { Agent, WebResourceLike, HttpHeadersLike, RawHttpHeaders, HttpHeader, TransferProgressEvent, toHttpHeadersLike, } from "./util.js";
//# sourceMappingURL=index.d.ts.map