# Azure Core LRO client library for JavaScript

This is the default implementation of long running operations in Azure SDK JavaScript client libraries which work in both the browser and NodeJS. This library is primarily intended to be used in code generated by [AutoRest](https://github.com/Azure/Autorest) and [`autorest.typescript`](https://github.com/Azure/autorest.typescript).

`@azure/core-lro` follows [The Azure SDK Design Guidelines for Long Running Operations](https://azure.github.io/azure-sdk/typescript_design.html#ts-lro)

Key links:

- [Source code](https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/core/core-lro)
- [Package (npm)](https://www.npmjs.com/package/@azure/core-lro) 
- [API Reference Documentation](https://docs.microsoft.com/javascript/api/@azure/core-lro) 
- [Samples](https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-lro/samples)

## Getting started

### Currently supported environments

- [LTS versions of Node.js](https://github.com/nodejs/release#release-schedule)
- Latest versions of Safari, Chrome, Edge, and Firefox.

### Installation

This package is primarily used in generated code and not meant to be consumed directly by end users.

## Key concepts

### `SimplePollerLike`

A poller is an object that can poll the long running operation on the server for its state until it reaches a terminal state. It provides the following methods:

- `getOperationState`: returns the state of the operation, typed as a type that extends `OperationState`
- `getResult`: returns the result of the operation when it completes and `undefined` otherwise
- `isDone`: returns whether the operation is in a terminal state
- `isStopped`: returns whether the polling stopped
- `onProgress`: registers callback functions to be called every time a polling response is received
- `poll`: sends a single polling request
- `pollUntilDone`: returns a promise that will resolve with the result of the operation
- `stopPolling`: stops polling;
- `toString`: serializes the state of the poller

### `OperationState`

A type for the operation state. It contains a `status` field with the following possible values: `notStarted`, `running`, `succeeded`, `failed`, and `canceled`. It can be accessed as follows:

```typescript
switch(poller.getOperationState().status) {
  case "succeeded":  // return poller.getResult();
  case "failed":     // throw poller.getOperationState().error;
  case "canceled":   // throw new Error("Operation was canceled");
  case "running":    // ...
  case "notStarted": // ...
}
```

### `createHttpPoller`

A function that returns an object of type `SimplePollerLike`. This poller behaves as follows in the presence of errors:

- calls to `poll` and `pollUntilDone` will throw an error in case the operation has failed or canceled unless the `resolveOnUnsuccessful` option was set to true.
- `poller.getOperationState().status` will be set to true when either the operation fails or it returns an error response.


## Examples

Examples can be found in the `samples` folder.

## Troubleshooting

### Logging

Logs can be added at the discretion of the library implementing the Long Running Operation poller.
Packages inside of [azure-sdk-for-js](https://github.com/Azure/azure-sdk-for-js) use
[@azure/logger](https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/core/logger).

## Next steps

Please take a look at the [samples](https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-lro/samples) directory for detailed examples on how to use this library.

## Contributing

If you'd like to contribute to this library, please read the [contributing guide](https://github.com/Azure/azure-sdk-for-js/blob/main/CONTRIBUTING.md) to learn more about how to build and test the code.

### Testing

To run our tests, first install the dependencies (with `npm install` or `rush install`),
then run the unit tests with: `npm run unit-test`.

### Code of Conduct

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or
contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.

![Impressions](https://azure-sdk-impressions.azurewebsites.net/api/impressions/azure-sdk-for-js%2Fsdk%2Fcore%2Fcore-lro%2FREADME.png)
