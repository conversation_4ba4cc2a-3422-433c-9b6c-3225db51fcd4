{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/logger.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,0CAAmD;AAEnD;;;GAGG;AACU,QAAA,MAAM,GAAG,IAAA,2BAAkB,EAAC,UAAU,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The `@azure/logger` configuration for this package.\n * @internal\n */\nexport const logger = createClientLogger(\"core-lro\");\n"]}