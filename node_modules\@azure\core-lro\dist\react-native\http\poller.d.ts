import { LongRunningOperation } from "./models.js";
import { OperationState, SimplePollerLike } from "../poller/models.js";
import { CreateHttpPollerOptions } from "./models.js";
/**
 * Creates a poller that can be used to poll a long-running operation.
 * @param lro - Description of the long-running operation
 * @param options - options to configure the poller
 * @returns an initialized poller
 */
export declare function createHttpPoller<TResult, TState extends OperationState<TResult>>(lro: LongRunningOperation, options?: CreateHttpPollerOptions<TResult, TState>): Promise<SimplePollerLike<TState, TResult>>;
//# sourceMappingURL=poller.d.ts.map