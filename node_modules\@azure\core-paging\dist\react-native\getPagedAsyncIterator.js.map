{"version": 3, "file": "getPagedAsyncIterator.js", "sourceRoot": "", "sources": ["../../src/getPagedAsyncIterator.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAIlC;;;;;;GAMG;AAEH,MAAM,UAAU,qBAAqB,CAMnC,WAAqD;;IAErD,MAAM,IAAI,GAAG,oBAAoB,CAAwC,WAAW,CAAC,CAAC;IACtF,OAAO;QACL,IAAI;YACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,EACJ,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,mCAClB,CAAC,CAAC,QAAuB,EAAE,EAAE;YAC5B,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC;YAC1D,OAAO,oBAAoB,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,iBAAiD;gBAC3D,WAAW;aACZ,CAAC,CAAC;QACL,CAAC,CAA2E;KAC/E,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,WAAqD;;;QAErD,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,cAAM,KAAK,CAAC,IAAI,EAAE,CAAA,CAAC;QACpC,6FAA6F;QAC7F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,sCAAsC;YACtC,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;YACnC,IAAI,UAAU,EAAE,CAAC;gBACf,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAe,CAAA,CAAA,CAAA,CAAC;;oBAChD,KAAyB,eAAA,UAAA,cAAA,KAAK,CAAA,WAAA,kFAAE,CAAC;wBAAR,qBAAK;wBAAL,WAAK;wBAAnB,MAAM,IAAI,KAAA,CAAA;wBACnB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,UAAU,CAAC,IAAI,CAAe,CAAA,CAAA,CAAA,CAAC;oBACxC,CAAC;;;;;;;;;YACH,CAAC;iBAAM,CAAC;gBACN,oBAAM,QAAQ,CAAC,KAAK,CAAA,CAAC;gBACrB,sFAAsF;gBACtF,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,KAAmD,CAAA,CAAA,CAAA,CAAC;YAC7D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,QAAQ,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;;gBACtB,KAAyB,eAAA,UAAA,cAAA,KAAK,CAAA,WAAA,kFAAE,CAAC;oBAAR,qBAAK;oBAAL,WAAK;oBAAnB,MAAM,IAAI,KAAA,CAAA;oBACnB,gGAAgG;oBAChG,gDAAgD;oBAChD,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,IAA6B,CAAA,CAAA,CAAA,CAAC;gBACvC,CAAC;;;;;;;;;QACH,CAAC;IACH,CAAC;CAAA;AAED,SAAgB,oBAAoB,CAClC,WAAqD,EACrD,UAGI,EAAE;;QAEN,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAC1C,IAAI,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA,CAAC;QAC7F,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,6BAAO;QACT,CAAC;QACD,oBAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;QACpB,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC7B,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA,CAAC;YACzE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,6BAAO;YACT,CAAC;YACD,oBAAM,QAAQ,CAAC,IAAI,CAAA,CAAC;QACtB,CAAC;IACH,CAAC;CAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PageSettings, PagedAsyncIterableIterator, PagedResult } from \"./models.js\";\n\n/**\n * returns an async iterator that iterates over results. It also has a `byPage`\n * method that returns pages of items at once.\n *\n * @param pagedResult - an object that specifies how to get pages.\n * @returns a paged async iterator that iterates over results.\n */\n\nexport function getPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings = PageSettings,\n  TLink = string,\n>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const iter = getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(pagedResult);\n  return {\n    next() {\n      return iter.next();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    byPage:\n      pagedResult?.byPage ??\n      (((settings?: PageSettings) => {\n        const { continuationToken, maxPageSize } = settings ?? {};\n        return getPageAsyncIterator(pagedResult, {\n          pageLink: continuationToken as unknown as TLink | undefined,\n          maxPageSize,\n        });\n      }) as unknown as (settings?: TPageSettings) => AsyncIterableIterator<TPage>),\n  };\n}\n\nasync function* getItemAsyncIterator<TElement, TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n): AsyncIterableIterator<TElement> {\n  const pages = getPageAsyncIterator(pagedResult);\n  const firstVal = await pages.next();\n  // if the result does not have an array shape, i.e. TPage = TElement, then we return it as is\n  if (!Array.isArray(firstVal.value)) {\n    // can extract elements from this page\n    const { toElements } = pagedResult;\n    if (toElements) {\n      yield* toElements(firstVal.value) as TElement[];\n      for await (const page of pages) {\n        yield* toElements(page) as TElement[];\n      }\n    } else {\n      yield firstVal.value;\n      // `pages` is of type `AsyncIterableIterator<TPage>` but TPage = TElement in this case\n      yield* pages as unknown as AsyncIterableIterator<TElement>;\n    }\n  } else {\n    yield* firstVal.value;\n    for await (const page of pages) {\n      // pages is of type `AsyncIterableIterator<TPage>` so `page` is of type `TPage`. In this branch,\n      // it must be the case that `TPage = TElement[]`\n      yield* page as unknown as TElement[];\n    }\n  }\n}\n\nasync function* getPageAsyncIterator<TPage, TLink, TPageSettings>(\n  pagedResult: PagedResult<TPage, TPageSettings, TLink>,\n  options: {\n    maxPageSize?: number;\n    pageLink?: TLink;\n  } = {},\n): AsyncIterableIterator<TPage> {\n  const { pageLink, maxPageSize } = options;\n  let response = await pagedResult.getPage(pageLink ?? pagedResult.firstPageLink, maxPageSize);\n  if (!response) {\n    return;\n  }\n  yield response.page;\n  while (response.nextPageLink) {\n    response = await pagedResult.getPage(response.nextPageLink, maxPageSize);\n    if (!response) {\n      return;\n    }\n    yield response.page;\n  }\n}\n"]}