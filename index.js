// Import the express library
const express = require('express');
const sql = require('mssql');
const dbConfig = require('./dbConfig');

// Create an instance of an Express application
const app = express();
const port = 3000;

// Main function to connect to the database and then start the server
async function startApp() {
  try {
    // Establish the database connection pool.
    // It's better to await the connection here so the server doesn't start if the DB is down.
    const pool = await new sql.ConnectionPool(dbConfig).connect();
    console.log('Connected to MSSQL');

    // Define a route for the root URL ('/')
    app.get('/', async (req, res) => {
      try {
        // The pool is already connected and available here.
        // Let's first check what tables are available in the ECOMMERCE_DB
        const result = await pool.request()
          .query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'");

        res.json(result.recordset);
      } catch (err) {
        console.error('SQL query error', err);
        res.status(500).send({ message: 'Database query failed', error: err.message });
      }
    });

    // Start the server only after the DB connection is successful
    app.listen(port, () => {
      console.log(`Example app listening at http://localhost:${port}`);
    });
  } catch (err) {
    console.error('Database Connection Failed! Server not started.', err);
    process.exit(1); // Exit the process with an error code
  }
}

startApp();
