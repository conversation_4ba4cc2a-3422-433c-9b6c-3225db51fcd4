"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHttpPoller = void 0;
const tslib_1 = require("tslib");
var poller_js_1 = require("./http/poller.js");
Object.defineProperty(exports, "createHttpPoller", { enumerable: true, get: function () { return poller_js_1.createHttpPoller; } });
/**
 * This can be uncommented to expose the protocol-agnostic poller
 */
// export {
//   BuildCreatePollerOptions,
//   Operation,
//   CreatePollerOptions,
//   OperationConfig,
//   RestorableOperationState,
// } from "./poller/models";
// export { buildCreatePoller } from "./poller/poller";
/** legacy */
tslib_1.__exportStar(require("./legacy/lroEngine/index.js"), exports);
tslib_1.__exportStar(require("./legacy/poller.js"), exports);
tslib_1.__exportStar(require("./legacy/pollOperation.js"), exports);
//# sourceMappingURL=index.js.map