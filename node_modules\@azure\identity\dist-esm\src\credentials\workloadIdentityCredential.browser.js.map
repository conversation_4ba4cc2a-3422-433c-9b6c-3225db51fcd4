{"version": 3, "file": "workloadIdentityCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/workloadIdentityCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAEhE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,6DAA6D,CAC9D,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D;;;;GAIG;AACH,MAAM,OAAO,0BAA0B;IACrC;;OAEG;IACH;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging\";\n\nconst BrowserNotSupportedError = new Error(\n  \"WorkloadIdentityCredential is not supported in the browser.\"\n);\nconst logger = credentialLogger(\"WorkloadIdentityCredential\");\n\n/**\n * WorkloadIdentityCredential supports Azure workload identity authentication on Kubernetes.\n * Refer to <a href=\"https://learn.microsoft.com/azure/aks/workload-identity-overview\">Azure Active Directory Workload Identity</a>\n * for more information.\n */\nexport class WorkloadIdentityCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  /**\n   * Only available in Node.js\n   */\n  public getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}