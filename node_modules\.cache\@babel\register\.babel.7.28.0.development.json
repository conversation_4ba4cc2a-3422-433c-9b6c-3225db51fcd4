{"{\"assumptions\":{},\"sourceRoot\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\\",\"caller\":{\"name\":\"@babel/register\"},\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\",\"filename\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\server.js\",\"targets\":{},\"cloneInputAst\":true,\"babelrc\":false,\"configFile\":false,\"browserslistConfigFile\":false,\"passPerPreset\":false,\"envName\":\"development\",\"root\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\",\"rootMode\":\"root\",\"plugins\":[{\"key\":\"transform-react-jsx\",\"visitor\":{\"JSXNamespacedName\":{\"enter\":[null]},\"JSXSpreadChild\":{\"enter\":[null]},\"Program\":{\"enter\":[null]},\"JSXFragment\":{\"exit\":[null]},\"JSXElement\":{\"exit\":[null]},\"JSXAttribute\":{\"enter\":[null]}},\"options\":{\"pragma\":\"React.createElement\",\"pragmaFrag\":\"React.Fragment\",\"runtime\":\"classic\",\"throwIfNamespace\":true,\"useBuiltIns\":false},\"externalDependencies\":[]},{\"key\":\"transform-react-display-name\",\"visitor\":{\"ExportDefaultDeclaration\":{\"enter\":[null]},\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]},{\"key\":\"transform-react-pure-annotations\",\"visitor\":{\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]}],\"presets\":[]}:7.28.0:development": {"value": {"code": "const express = require('express');\nconst React = require('react');\nconst ReactDOMServer = require('react-dom/server');\nconst HelloWorld2 = require('./HelloWorld2');\nconst app = express();\nconst PORT = 3000;\napp.get('/', (req, res) => {\n  // 1. Render our React component to an HTML string.\n  // The JSX here is transpiled on the fly by @babel/register\n  const appString = ReactDOMServer.renderToString(/*#__PURE__*/React.createElement(HelloWorld2, null));\n\n  // 2. Create the full HTML document and inject the rendered string.\n  const html = `\n        <!DOCTYPE html>\n        <html lang=\"en\">\n        <head>\n            <meta charset=\"UTF-8\">\n            <title>Hello React SSR!</title>\n        </head>\n        <body>\n            <div id=\"root\">${appString}</div>\n        </body>\n        </html>\n    `;\n\n  // 3. Send the complete HTML page to the client.\n  res.send(html);\n});\napp.listen(PORT, () => {\n  console.log(`Server-side rendering app listening at http://localhost:${PORT}`);\n});\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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", "map": {"version": 3, "names": ["express", "require", "React", "ReactDOMServer", "HelloWorld2", "app", "PORT", "get", "req", "res", "appString", "renderToString", "createElement", "html", "send", "listen", "console", "log"], "sourceRoot": "C:\\Users\\<USER>\\Documents\\GitHub\\git-Basic-React\\", "sources": ["server.js"], "sourcesContent": ["const express = require('express');\r\nconst React = require('react');\r\nconst ReactDOMServer = require('react-dom/server');\r\nconst HelloWorld2 = require('./HelloWorld2');\r\n\r\nconst app = express();\r\nconst PORT = 3000;\r\n\r\napp.get('/', (req, res) => {\r\n    // 1. Render our React component to an HTML string.\r\n    // The JSX here is transpiled on the fly by @babel/register\r\n    const appString = ReactDOMServer.renderToString(<HelloWorld2 />);\r\n\r\n    // 2. Create the full HTML document and inject the rendered string.\r\n    const html = `\r\n        <!DOCTYPE html>\r\n        <html lang=\"en\">\r\n        <head>\r\n            <meta charset=\"UTF-8\">\r\n            <title>Hello React SSR!</title>\r\n        </head>\r\n        <body>\r\n            <div id=\"root\">${appString}</div>\r\n        </body>\r\n        </html>\r\n    `;\r\n\r\n    // 3. Send the complete HTML page to the client.\r\n    res.send(html);\r\n});\r\n\r\napp.listen(PORT, () => {\r\n    console.log(`Server-side rendering app listening at http://localhost:${PORT}`);\r\n});"], "mappings": "AAAA,MAAMA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAClD,MAAMG,WAAW,GAAGH,OAAO,CAAC,eAAe,CAAC;AAE5C,MAAMI,GAAG,GAAGL,OAAO,CAAC,CAAC;AACrB,MAAMM,IAAI,GAAG,IAAI;AAEjBD,GAAG,CAACE,GAAG,CAAC,GAAG,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACvB;EACA;EACA,MAAMC,SAAS,GAAGP,cAAc,CAACQ,cAAc,cAACT,KAAA,CAAAU,aAAA,CAACR,WAAW,MAAE,CAAC,CAAC;;EAEhE;EACA,MAAMS,IAAI,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6BH,SAAS;AACtC;AACA;AACA,KAAK;;EAED;EACAD,GAAG,CAACK,IAAI,CAACD,IAAI,CAAC;AAClB,CAAC,CAAC;AAEFR,GAAG,CAACU,MAAM,CAACT,IAAI,EAAE,MAAM;EACnBU,OAAO,CAACC,GAAG,CAAC,2DAA2DX,IAAI,EAAE,CAAC;AAClF,CAAC,CAAC", "ignoreList": []}}, "mtime": 1754490428165}, "{\"assumptions\":{},\"sourceRoot\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\\",\"caller\":{\"name\":\"@babel/register\"},\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\",\"filename\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\HelloWorld2.js\",\"targets\":{},\"cloneInputAst\":true,\"babelrc\":false,\"configFile\":false,\"browserslistConfigFile\":false,\"passPerPreset\":false,\"envName\":\"development\",\"root\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\",\"rootMode\":\"root\",\"plugins\":[{\"key\":\"transform-react-jsx\",\"visitor\":{\"JSXNamespacedName\":{\"enter\":[null]},\"JSXSpreadChild\":{\"enter\":[null]},\"Program\":{\"enter\":[null]},\"JSXFragment\":{\"exit\":[null]},\"JSXElement\":{\"exit\":[null]},\"JSXAttribute\":{\"enter\":[null]}},\"options\":{\"pragma\":\"React.createElement\",\"pragmaFrag\":\"React.Fragment\",\"runtime\":\"classic\",\"throwIfNamespace\":true,\"useBuiltIns\":false},\"externalDependencies\":[]},{\"key\":\"transform-react-display-name\",\"visitor\":{\"ExportDefaultDeclaration\":{\"enter\":[null]},\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]},{\"key\":\"transform-react-pure-annotations\",\"visitor\":{\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]}],\"presets\":[]}:7.28.0:development": {"value": {"code": "const React = require('react');\nfunction HelloWorld2() {\n  return /*#__PURE__*/React.createElement(\"h1\", null, \"Hello, World! 2 (Rendered on the Server)\");\n}\nmodule.exports = HelloWorld2;\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdCIsInJlcXVpcmUiLCJIZWxsb1dvcmxkMiIsImNyZWF0ZUVsZW1lbnQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiJDOlxcVXNlcnNcXGhlbGxvXFxEb2N1bWVudHNcXEdpdEh1YlxcZ2l0LUJhc2ljLVJlYWN0XFwiLCJzb3VyY2VzIjpbIkhlbGxvV29ybGQyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcclxuXHJcbmZ1bmN0aW9uIEhlbGxvV29ybGQyKCkge1xyXG4gICAgcmV0dXJuIDxoMT5IZWxsbywgV29ybGQhIDIgKFJlbmRlcmVkIG9uIHRoZSBTZXJ2ZXIpPC9oMT47XHJcbn1cclxuXHJcbm1vZHVsZS5leHBvcnRzID0gSGVsbG9Xb3JsZDI7Il0sIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxLQUFLLEdBQUdDLE9BQU8sQ0FBQyxPQUFPLENBQUM7QUFFOUIsU0FBU0MsV0FBV0EsQ0FBQSxFQUFHO0VBQ25CLG9CQUFPRixLQUFBLENBQUFHLGFBQUEsYUFBSSwwQ0FBNEMsQ0FBQztBQUM1RDtBQUVBQyxNQUFNLENBQUNDLE9BQU8sR0FBR0gsV0FBVyIsImlnbm9yZUxpc3QiOltdfQ==", "map": {"version": 3, "names": ["React", "require", "HelloWorld2", "createElement", "module", "exports"], "sourceRoot": "C:\\Users\\<USER>\\Documents\\GitHub\\git-Basic-React\\", "sources": ["HelloWorld2.js"], "sourcesContent": ["const React = require('react');\r\n\r\nfunction HelloWorld2() {\r\n    return <h1>Hello, World! 2 (Rendered on the Server)</h1>;\r\n}\r\n\r\nmodule.exports = HelloWorld2;"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE9B,SAASC,WAAWA,CAAA,EAAG;EACnB,oBAAOF,KAAA,CAAAG,aAAA,aAAI,0CAA4C,CAAC;AAC5D;AAEAC,MAAM,CAACC,OAAO,GAAGH,WAAW", "ignoreList": []}}, "mtime": 1754490331343}, "{\"assumptions\":{},\"sourceRoot\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\\\\\",\"caller\":{\"name\":\"@babel/register\"},\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\",\"filename\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\\\\server.js\",\"targets\":{},\"cloneInputAst\":true,\"babelrc\":false,\"configFile\":false,\"browserslistConfigFile\":false,\"passPerPreset\":false,\"envName\":\"development\",\"root\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\",\"rootMode\":\"root\",\"plugins\":[{\"key\":\"transform-react-jsx\",\"visitor\":{\"JSXNamespacedName\":{\"enter\":[null]},\"JSXSpreadChild\":{\"enter\":[null]},\"Program\":{\"enter\":[null]},\"JSXFragment\":{\"exit\":[null]},\"JSXElement\":{\"exit\":[null]},\"JSXAttribute\":{\"enter\":[null]}},\"options\":{\"pragma\":\"React.createElement\",\"pragmaFrag\":\"React.Fragment\",\"runtime\":\"classic\",\"throwIfNamespace\":true,\"useBuiltIns\":false},\"externalDependencies\":[]},{\"key\":\"transform-react-display-name\",\"visitor\":{\"ExportDefaultDeclaration\":{\"enter\":[null]},\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]},{\"key\":\"transform-react-pure-annotations\",\"visitor\":{\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]}],\"presets\":[]}:7.28.0:development": {"value": {"code": "const express = require('express');\nconst React = require('react');\nconst ReactDOMServer = require('react-dom/server');\nconst HelloWorld2 = require('./HelloWorld2');\nconst app = express();\nconst PORT = 3000;\napp.get('/', (req, res) => {\n  // 1. Render our React component to an HTML string.\n  // The JSX here is transpiled on the fly by @babel/register\n  const appString = ReactDOMServer.renderToString(/*#__PURE__*/React.createElement(HelloWorld2, null));\n\n  // 2. Create the full HTML document and inject the rendered string.\n  const html = `\n        <!DOCTYPE html>\n        <html lang=\"en\">\n        <head>\n            <meta charset=\"UTF-8\">\n            <title>Hello React SSR!</title>\n        </head>\n        <body>\n            <div id=\"root\">${appString}</div>\n        </body>\n        </html>\n    `;\n\n  // 3. Send the complete HTML page to the client.\n  res.send(html);\n});\napp.listen(PORT, () => {\n  console.log(`Server-side rendering app listening at http://localhost:${PORT}`);\n});\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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", "map": {"version": 3, "names": ["express", "require", "React", "ReactDOMServer", "HelloWorld2", "app", "PORT", "get", "req", "res", "appString", "renderToString", "createElement", "html", "send", "listen", "console", "log"], "sourceRoot": "C:\\Users\\<USER>\\Documents\\GitHub\\git-Basic-React\\basic server with html react js - JSX\\", "sources": ["server.js"], "sourcesContent": ["const express = require('express');\r\nconst React = require('react');\r\nconst ReactDOMServer = require('react-dom/server');\r\nconst HelloWorld2 = require('./HelloWorld2');\r\n\r\nconst app = express();\r\nconst PORT = 3000;\r\n\r\napp.get('/', (req, res) => {\r\n    // 1. Render our React component to an HTML string.\r\n    // The JSX here is transpiled on the fly by @babel/register\r\n    const appString = ReactDOMServer.renderToString(<HelloWorld2 />);\r\n\r\n    // 2. Create the full HTML document and inject the rendered string.\r\n    const html = `\r\n        <!DOCTYPE html>\r\n        <html lang=\"en\">\r\n        <head>\r\n            <meta charset=\"UTF-8\">\r\n            <title>Hello React SSR!</title>\r\n        </head>\r\n        <body>\r\n            <div id=\"root\">${appString}</div>\r\n        </body>\r\n        </html>\r\n    `;\r\n\r\n    // 3. Send the complete HTML page to the client.\r\n    res.send(html);\r\n});\r\n\r\napp.listen(PORT, () => {\r\n    console.log(`Server-side rendering app listening at http://localhost:${PORT}`);\r\n});"], "mappings": "AAAA,MAAMA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAClD,MAAMG,WAAW,GAAGH,OAAO,CAAC,eAAe,CAAC;AAE5C,MAAMI,GAAG,GAAGL,OAAO,CAAC,CAAC;AACrB,MAAMM,IAAI,GAAG,IAAI;AAEjBD,GAAG,CAACE,GAAG,CAAC,GAAG,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACvB;EACA;EACA,MAAMC,SAAS,GAAGP,cAAc,CAACQ,cAAc,cAACT,KAAA,CAAAU,aAAA,CAACR,WAAW,MAAE,CAAC,CAAC;;EAEhE;EACA,MAAMS,IAAI,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6BH,SAAS;AACtC;AACA;AACA,KAAK;;EAED;EACAD,GAAG,CAACK,IAAI,CAACD,IAAI,CAAC;AAClB,CAAC,CAAC;AAEFR,GAAG,CAACU,MAAM,CAACT,IAAI,EAAE,MAAM;EACnBU,OAAO,CAACC,GAAG,CAAC,2DAA2DX,IAAI,EAAE,CAAC;AAClF,CAAC,CAAC", "ignoreList": []}}, "mtime": 1754490428165}, "{\"assumptions\":{},\"sourceRoot\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\\\\\",\"caller\":{\"name\":\"@babel/register\"},\"cwd\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\",\"filename\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\\\\HelloWorld2.js\",\"targets\":{},\"cloneInputAst\":true,\"babelrc\":false,\"configFile\":false,\"browserslistConfigFile\":false,\"passPerPreset\":false,\"envName\":\"development\",\"root\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\git-Basic-React\\\\basic server with html react js - JSX\",\"rootMode\":\"root\",\"plugins\":[{\"key\":\"transform-react-jsx\",\"visitor\":{\"JSXNamespacedName\":{\"enter\":[null]},\"JSXSpreadChild\":{\"enter\":[null]},\"Program\":{\"enter\":[null]},\"JSXFragment\":{\"exit\":[null]},\"JSXElement\":{\"exit\":[null]},\"JSXAttribute\":{\"enter\":[null]}},\"options\":{\"pragma\":\"React.createElement\",\"pragmaFrag\":\"React.Fragment\",\"runtime\":\"classic\",\"throwIfNamespace\":true,\"useBuiltIns\":false},\"externalDependencies\":[]},{\"key\":\"transform-react-display-name\",\"visitor\":{\"ExportDefaultDeclaration\":{\"enter\":[null]},\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]},{\"key\":\"transform-react-pure-annotations\",\"visitor\":{\"CallExpression\":{\"enter\":[null]},\"_exploded\":true,\"_verified\":true},\"options\":{},\"externalDependencies\":[]}],\"presets\":[]}:7.28.0:development": {"value": {"code": "const React = require('react');\nfunction HelloWorld2() {\n  return /*#__PURE__*/React.createElement(\"h1\", null, \"Hello, World! 2 (Rendered on the Server)\");\n}\nmodule.exports = HelloWorld2;\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdCIsInJlcXVpcmUiLCJIZWxsb1dvcmxkMiIsImNyZWF0ZUVsZW1lbnQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiJDOlxcVXNlcnNcXGhlbGxvXFxEb2N1bWVudHNcXEdpdEh1YlxcZ2l0LUJhc2ljLVJlYWN0XFxiYXNpYyBzZXJ2ZXIgd2l0aCBodG1sIHJlYWN0IGpzIC0gSlNYXFwiLCJzb3VyY2VzIjpbIkhlbGxvV29ybGQyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcclxuXHJcbmZ1bmN0aW9uIEhlbGxvV29ybGQyKCkge1xyXG4gICAgcmV0dXJuIDxoMT5IZWxsbywgV29ybGQhIDIgKFJlbmRlcmVkIG9uIHRoZSBTZXJ2ZXIpPC9oMT47XHJcbn1cclxuXHJcbm1vZHVsZS5leHBvcnRzID0gSGVsbG9Xb3JsZDI7Il0sIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxLQUFLLEdBQUdDLE9BQU8sQ0FBQyxPQUFPLENBQUM7QUFFOUIsU0FBU0MsV0FBV0EsQ0FBQSxFQUFHO0VBQ25CLG9CQUFPRixLQUFBLENBQUFHLGFBQUEsYUFBSSwwQ0FBNEMsQ0FBQztBQUM1RDtBQUVBQyxNQUFNLENBQUNDLE9BQU8sR0FBR0gsV0FBVyIsImlnbm9yZUxpc3QiOltdfQ==", "map": {"version": 3, "names": ["React", "require", "HelloWorld2", "createElement", "module", "exports"], "sourceRoot": "C:\\Users\\<USER>\\Documents\\GitHub\\git-Basic-React\\basic server with html react js - JSX\\", "sources": ["HelloWorld2.js"], "sourcesContent": ["const React = require('react');\r\n\r\nfunction HelloWorld2() {\r\n    return <h1>Hello, World! 2 (Rendered on the Server)</h1>;\r\n}\r\n\r\nmodule.exports = HelloWorld2;"], "mappings": "AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE9B,SAASC,WAAWA,CAAA,EAAG;EACnB,oBAAOF,KAAA,CAAAG,aAAA,aAAI,0CAA4C,CAAC;AAC5D;AAEAC,MAAM,CAACC,OAAO,GAAGH,WAAW", "ignoreList": []}}, "mtime": 1754490331343}}