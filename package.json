{"name": "react-ssr-example", "version": "1.0.0", "description": "A basic server-side rendering example with React and Express.", "main": "server.js", "scripts": {"start": "node server-runner.js", "start:simple": "node index.js", "start:mock": "node index-no-db.js"}, "dependencies": {"express": "^4.18.2", "mssql": "^10.0.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/register": "^7.22.15"}}