{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/legacy/models.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { CancelOnProgress } from \"../poller/models.js\";\nimport { PollOperationState } from \"./pollOperation.js\";\n\n/**\n * Abstract representation of a poller, intended to expose just the minimal API that the user needs to work with.\n */\n// eslint-disable-next-line no-use-before-define\nexport interface PollerLike<TState extends PollOperationState<TResult>, TResult> {\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   */\n  poll(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  pollUntilDone(pollOptions?: { abortSignal?: AbortSignalLike }): Promise<TResult>;\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  onProgress(callback: (state: TState) => void): CancelOnProgress;\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  isDone(): boolean;\n  /**\n   * Stops the poller. After this, no manual or automated requests can be sent.\n   */\n  stopPolling(): void;\n  /**\n   * Returns true if the poller is stopped.\n   */\n  isStopped(): boolean;\n  /**\n   * Attempts to cancel the underlying operation.\n   * @deprecated `cancelOperation` has been deprecated because it was not implemented.\n   */\n  cancelOperation(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns the state of the operation.\n   * The TState defined in PollerLike can be a subset of the TState defined in\n   * the Poller implementation.\n   */\n  getOperationState(): TState;\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  getResult(): TResult | undefined;\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  toString(): string;\n}\n"]}