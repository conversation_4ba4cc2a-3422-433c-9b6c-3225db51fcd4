const config = {
    user: 'cryptouser2',          // TODO: Replace with your SQL Server username
    password: 'senators',      // TODO: Replace with your SQL Server password
    server: 'localhost',            // Try localhost instead of computer name
    database: 'crypto', // TODO: Replace with your database name
    options: {
        encrypt: false, // Use this if you're on Azure
        trustServerCertificate: true, // Change to true for local dev / self-signed certs
        enableArithAbort: true,
        instanceName: 'MSSQLSERVER' // Try specifying the instance name
    }
};

module.exports = config;


